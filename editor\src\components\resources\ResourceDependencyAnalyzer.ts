/**
 * 资源依赖分析器
 * 用于分析资源之间的依赖关系，提供可视化和优化建议
 */
import { Node, Edge, Position } from 'reactflow';
import dagre from 'dagre';
import { message } from 'antd';
import { AssetType, DependencyType } from '../../libs/dl-engine';

// 资源服务接口（实际项目中应从服务层导入）
interface ResourceService {
  getResources(): Promise<ResourceInfo[]>;
  getResourceById(id: string): Promise<ResourceInfo | null>;
  getDependencies(resourceId: string): Promise<DependencyInfo[]>;
  getDependents(resourceId: string): Promise<DependencyInfo[]>;
  getResourceUsage(resourceId: string): Promise<ResourceUsageInfo>;
}

// 资源信息接口
export interface ResourceInfo {
  id: string;
  name: string;
  type: AssetType;
  url: string;
  size: number;
  lastModified: number;
  metadata?: Record<string, any>;
}

// 依赖信息接口
export interface DependencyInfo {
  id: string;
  type: DependencyType;
  priority?: number;
  metadata?: Record<string, any>;
}

// 资源使用信息接口
export interface ResourceUsageInfo {
  refCount: number;
  lastAccessed: number;
  loadCount: number;
  loadTime: number;
  usedIn: string[];
}

// 分析选项接口
export interface AnalysisOptions {
  includeTypes?: DependencyType[];
  showAllResources?: boolean;
  filterType?: string;
  searchValue?: string;
  layoutType?: 'dagre' | 'force' | 'radial';
  maxDepth?: number;
}

// 分析结果接口
export interface AnalysisResult {
  nodes: Node[];
  edges: Edge[];
  analysis: {
    totalResources: number;
    totalDependencies: number;
    circularDependencies: string[][];
    unusedResources: string[];
    duplicateResources: string[][];
    largeResources: string[];
    resourcesByType: Record<string, number>;
    dependencyChains: string[][];
    loadOrder: string[];
    memoryUsage: number;
    resourceSizes: Record<string, number>;
  };
  suggestions: OptimizationSuggestion[];
}

// 优化建议接口
export interface OptimizationSuggestion {
  id: string;
  type: 'circular' | 'unused' | 'duplicate' | 'large' | 'lazy' | 'merge' | 'split';
  severity: 'low' | 'medium' | 'high';
  description: string;
  resources: string[];
  impact: string;
  solution: string;
}

/**
 * 资源依赖分析器类
 */
export class ResourceDependencyAnalyzer {
  private resourceService: ResourceService;
  private cachedResources: Map<string, ResourceInfo> = new Map();
  private cachedDependencies: Map<string, DependencyInfo[]> = new Map();
  private cachedDependents: Map<string, DependencyInfo[]> = new Map();
  private cachedUsage: Map<string, ResourceUsageInfo> = new Map();

  /**
   * 构造函数
   * @param resourceService 资源服务
   */
  constructor(resourceService?: ResourceService) {
    // 临时模拟资源服务，实际项目中应注入真实服务
    this.resourceService = resourceService || this.createMockResourceService();
  }

  /**
   * 分析依赖关系
   * @param resourceId 资源ID
   * @param options 分析选项
   * @returns 分析结果
   */
  public async analyzeDependencies(
    resourceId: string | null,
    options: AnalysisOptions = {}
  ): Promise<AnalysisResult> {
    try {
      // 设置默认选项
      const defaultOptions: Required<AnalysisOptions> = {
        includeTypes: [DependencyType.STRONG, DependencyType.WEAK, DependencyType.LAZY],
        showAllResources: false,
        filterType: '',
        searchValue: '',
        layoutType: 'dagre',
        maxDepth: 10
      };

      const finalOptions: Required<AnalysisOptions> = { ...defaultOptions, ...options };

      // 获取所有资源
      const allResources = await this.getAllResources();

      // 过滤资源
      const filteredResources = this.filterResources(allResources, finalOptions);

      // 构建依赖图
      const { nodes, edges } = await this.buildDependencyGraph(resourceId, filteredResources, finalOptions);

      // 应用布局
      const { nodes: layoutedNodes, edges: layoutedEdges } = this.applyLayout(nodes, edges, finalOptions.layoutType);

      // 分析依赖关系
      const analysis = await this.analyzeGraph(layoutedNodes, layoutedEdges, filteredResources);

      // 生成优化建议
      const suggestions = this.generateOptimizationSuggestions(analysis, filteredResources);

      return {
        nodes: layoutedNodes,
        edges: layoutedEdges,
        analysis,
        suggestions
      };
    } catch (error) {
      console.error('分析依赖关系失败:', error);
      message.error('分析依赖关系失败，请稍后重试');
      throw error;
    }
  }

  /**
   * 创建模拟资源服务（临时使用，实际项目中应使用真实服务）
   * @returns 模拟资源服务
   */
  private createMockResourceService(): ResourceService {
    // 模拟资源数据
    const mockResources: ResourceInfo[] = [
      { id: 'texture1', name: '地面纹理', type: 'texture', url: '/textures/ground.jpg', size: 1024 * 1024 * 2, lastModified: Date.now() },
      { id: 'texture2', name: '墙壁纹理', type: 'texture', url: '/textures/wall.jpg', size: 1024 * 1024 * 1.5, lastModified: Date.now() },
      { id: 'model1', name: '角色模型', type: 'model', url: '/models/character.glb', size: 1024 * 1024 * 5, lastModified: Date.now() },
      { id: 'material1', name: '地面材质', type: 'material', url: '/materials/ground.mat', size: 1024 * 10, lastModified: Date.now() },
      { id: 'material2', name: '墙壁材质', type: 'material', url: '/materials/wall.mat', size: 1024 * 12, lastModified: Date.now() },
      { id: 'shader1', name: '基础着色器', type: 'shader', url: '/shaders/basic.shader', size: 1024 * 5, lastModified: Date.now() },
      { id: 'audio1', name: '背景音乐', type: 'audio', url: '/audio/background.mp3', size: 1024 * 1024 * 3, lastModified: Date.now() },
      { id: 'scene1', name: '主场景', type: 'scene', url: '/scenes/main.scene', size: 1024 * 50, lastModified: Date.now() },
    ];

    // 模拟依赖关系
    const mockDependencies: Record<string, DependencyInfo[]> = {
      'material1': [
        { id: 'texture1', type: DependencyType.STRONG, priority: 10 },
        { id: 'shader1', type: DependencyType.STRONG, priority: 5 }
      ],
      'material2': [
        { id: 'texture2', type: DependencyType.STRONG, priority: 10 },
        { id: 'shader1', type: DependencyType.STRONG, priority: 5 }
      ],
      'model1': [
        { id: 'material1', type: DependencyType.STRONG, priority: 8 },
        { id: 'material2', type: DependencyType.WEAK, priority: 3 }
      ],
      'scene1': [
        { id: 'model1', type: DependencyType.STRONG, priority: 10 },
        { id: 'audio1', type: DependencyType.LAZY, priority: 2 }
      ]
    };

    // 模拟被依赖关系
    const mockDependents: Record<string, string[]> = {};
    for (const [resourceId, deps] of Object.entries(mockDependencies)) {
      for (const dep of deps) {
        if (!mockDependents[dep.id]) {
          mockDependents[dep.id] = [];
        }
        mockDependents[dep.id].push(resourceId);
      }
    }

    return {
      getResources: async () => mockResources,
      getResourceById: async (id) => mockResources.find(r => r.id === id) || null,
      getDependencies: async (id) => mockDependencies[id] || [],
      getDependents: async (id) => {
        return (mockDependents[id] || []).map(depId => {
          const depInfo = mockDependencies[depId].find(d => d.id === id);
          return {
            id: depId,
            type: depInfo?.type || DependencyType.STRONG,
            priority: depInfo?.priority || 0
          };
        });
      },
      getResourceUsage: async (id) => ({
        refCount: Math.floor(Math.random() * 10),
        lastAccessed: Date.now() - Math.floor(Math.random() * 1000000),
        loadCount: Math.floor(Math.random() * 20),
        loadTime: Math.floor(Math.random() * 500),
        usedIn: ['场景1', '场景2']
      })
    };
  }

  /**
   * 获取所有资源
   * @returns 资源信息数组
   */
  private async getAllResources(): Promise<ResourceInfo[]> {
    try {
      const resources = await this.resourceService.getResources();

      // 更新缓存
      resources.forEach(resource => {
        this.cachedResources.set(resource.id, resource);
      });

      return resources;
    } catch (error) {
      console.error('获取资源列表失败:', error);
      return [];
    }
  }

  /**
   * 过滤资源
   * @param resources 资源列表
   * @param options 过滤选项
   * @returns 过滤后的资源列表
   */
  private filterResources(resources: ResourceInfo[], options: Required<AnalysisOptions>): ResourceInfo[] {
    return resources.filter(resource => {
      // 按类型过滤
      if (options.filterType && resource.type !== options.filterType) {
        return false;
      }

      // 按搜索值过滤
      if (options.searchValue) {
        const searchLower = options.searchValue.toLowerCase();
        return (
          resource.id.toLowerCase().includes(searchLower) ||
          resource.name.toLowerCase().includes(searchLower) ||
          resource.url.toLowerCase().includes(searchLower)
        );
      }

      return true;
    });
  }

  /**
   * 构建依赖图
   * @param rootId 根资源ID
   * @param resources 资源列表
   * @param options 选项
   * @returns 节点和边
   */
  private async buildDependencyGraph(
    rootId: string | null,
    resources: ResourceInfo[],
    options: Required<AnalysisOptions>
  ): Promise<{ nodes: Node[]; edges: Edge[] }> {
    const nodes: Node[] = [];
    const edges: Edge[] = [];
    const processedNodes = new Set<string>();
    const processedEdges = new Set<string>();

    // 如果显示所有资源
    if (options.showAllResources) {
      // 添加所有资源节点
      for (const resource of resources) {
        nodes.push(this.createResourceNode(resource));
        processedNodes.add(resource.id);
      }

      // 添加所有依赖边
      for (const resource of resources) {
        const dependencies = await this.getResourceDependencies(resource.id);

        for (const dep of dependencies) {
          // 如果依赖类型不在包含类型中，则跳过
          if (!options.includeTypes.includes(dep.type)) {
            continue;
          }

          // 如果依赖资源不在过滤后的资源中，则跳过
          if (!processedNodes.has(dep.id)) {
            continue;
          }

          const edgeId = `${resource.id}-${dep.id}`;

          // 如果边已处理，则跳过
          if (processedEdges.has(edgeId)) {
            continue;
          }

          edges.push(this.createDependencyEdge(resource.id, dep.id, dep.type));
          processedEdges.add(edgeId);
        }
      }
    } else if (rootId) {
      // 递归构建依赖图
      await this.buildDependencyGraphRecursive(
        rootId,
        nodes,
        edges,
        processedNodes,
        processedEdges,
        options,
        0
      );
    }

    return { nodes, edges };
  }

  /**
   * 递归构建依赖图
   * @param resourceId 资源ID
   * @param nodes 节点数组
   * @param edges 边数组
   * @param processedNodes 已处理节点集合
   * @param processedEdges 已处理边集合
   * @param options 选项
   * @param depth 当前深度
   */
  private async buildDependencyGraphRecursive(
    resourceId: string,
    nodes: Node[],
    edges: Edge[],
    processedNodes: Set<string>,
    processedEdges: Set<string>,
    options: Required<AnalysisOptions>,
    depth: number
  ): Promise<void> {
    // 如果超过最大深度，则停止递归
    if (depth > options.maxDepth) {
      return;
    }

    // 如果节点已处理，则跳过
    if (processedNodes.has(resourceId)) {
      return;
    }

    // 获取资源信息
    const resource = await this.getResourceById(resourceId);

    if (!resource) {
      return;
    }

    // 添加节点
    nodes.push(this.createResourceNode(resource));
    processedNodes.add(resourceId);

    // 获取依赖
    const dependencies = await this.getResourceDependencies(resourceId);

    // 处理依赖
    for (const dep of dependencies) {
      // 如果依赖类型不在包含类型中，则跳过
      if (!options.includeTypes.includes(dep.type)) {
        continue;
      }

      const edgeId = `${resourceId}-${dep.id}`;

      // 如果边已处理，则跳过
      if (processedEdges.has(edgeId)) {
        continue;
      }

      // 添加边
      edges.push(this.createDependencyEdge(resourceId, dep.id, dep.type));
      processedEdges.add(edgeId);

      // 递归处理依赖
      await this.buildDependencyGraphRecursive(
        dep.id,
        nodes,
        edges,
        processedNodes,
        processedEdges,
        options,
        depth + 1
      );
    }

    // 获取被依赖
    const dependents = await this.getResourceDependents(resourceId);

    // 处理被依赖
    for (const dep of dependents) {
      // 如果依赖类型不在包含类型中，则跳过
      if (!options.includeTypes.includes(dep.type)) {
        continue;
      }

      const edgeId = `${dep.id}-${resourceId}`;

      // 如果边已处理，则跳过
      if (processedEdges.has(edgeId)) {
        continue;
      }

      // 添加边
      edges.push(this.createDependencyEdge(dep.id, resourceId, dep.type));
      processedEdges.add(edgeId);

      // 递归处理被依赖
      await this.buildDependencyGraphRecursive(
        dep.id,
        nodes,
        edges,
        processedNodes,
        processedEdges,
        options,
        depth + 1
      );
    }
  }

  /**
   * 创建资源节点
   * @param resource 资源信息
   * @returns 节点
   */
  private createResourceNode(resource: ResourceInfo): Node {
    return {
      id: resource.id,
      type: 'resourceNode',
      data: {
        label: resource.name,
        type: resource.type,
        size: resource.size,
        url: resource.url,
        lastModified: resource.lastModified,
        metadata: resource.metadata
      },
      position: { x: 0, y: 0 }
    };
  }

  /**
   * 创建依赖边
   * @param sourceId 源资源ID
   * @param targetId 目标资源ID
   * @param type 依赖类型
   * @returns 边
   */
  private createDependencyEdge(sourceId: string, targetId: string, type: DependencyType): Edge {
    // 根据依赖类型设置边的样式
    let style = {};
    let label = '';
    let markerEnd = { type: 'arrow' as const };

    switch (type) {
      case DependencyType.STRONG:
        style = { stroke: '#1890ff', strokeWidth: 2 };
        label = '强依赖';
        break;
      case DependencyType.WEAK:
        style = { stroke: '#52c41a', strokeWidth: 1, strokeDasharray: '5,5' };
        label = '弱依赖';
        break;
      case DependencyType.LAZY:
        style = { stroke: '#faad14', strokeWidth: 1, strokeDasharray: '3,3' };
        label = '延迟依赖';
        break;
      default:
        style = { stroke: '#d9d9d9', strokeWidth: 1 };
        label = '未知依赖';
        break;
    }

    return {
      id: `${sourceId}-${targetId}`,
      source: sourceId,
      target: targetId,
      type: 'smoothstep',
      label,
      style,
      markerEnd,
      data: { type }
    };
  }

  /**
   * 应用布局
   * @param nodes 节点数组
   * @param edges 边数组
   * @param layoutType 布局类型
   * @returns 布局后的节点和边
   */
  private applyLayout(
    nodes: Node[],
    edges: Edge[],
    layoutType: 'dagre' | 'force' | 'radial'
  ): { nodes: Node[]; edges: Edge[] } {
    if (nodes.length === 0) {
      return { nodes, edges };
    }

    switch (layoutType) {
      case 'dagre':
        return this.applyDagreLayout(nodes, edges);
      case 'force':
        return this.applyForceLayout(nodes, edges);
      case 'radial':
        return this.applyRadialLayout(nodes, edges);
      default:
        return { nodes, edges };
    }
  }

  /**
   * 应用Dagre布局
   * @param nodes 节点数组
   * @param edges 边数组
   * @returns 布局后的节点和边
   */
  private applyDagreLayout(nodes: Node[], edges: Edge[]): { nodes: Node[]; edges: Edge[] } {
    const dagreGraph = new dagre.graphlib.Graph();
    dagreGraph.setDefaultEdgeLabel(() => ({}));
    dagreGraph.setGraph({ rankdir: 'TB', nodesep: 80, ranksep: 100 });

    // 添加节点
    nodes.forEach(node => {
      dagreGraph.setNode(node.id, { width: 172, height: 36 });
    });

    // 添加边
    edges.forEach(edge => {
      dagreGraph.setEdge(edge.source, edge.target);
    });

    // 计算布局
    dagre.layout(dagreGraph);

    // 更新节点位置
    const layoutedNodes = nodes.map(node => {
      const dagreNode = dagreGraph.node(node.id);

      return {
        ...node,
        position: {
          x: dagreNode.x - 86,
          y: dagreNode.y - 18
        },
        targetPosition: Position.Top,
        sourcePosition: Position.Bottom
      };
    });

    return { nodes: layoutedNodes, edges };
  }

  /**
   * 应用力导向布局
   * @param nodes 节点数组
   * @param edges 边数组
   * @returns 布局后的节点和边
   */
  private applyForceLayout(nodes: Node[], edges: Edge[]): { nodes: Node[]; edges: Edge[] } {
    // 简单实现，实际项目中可使用d3-force等库
    const centerX = 500;
    const centerY = 300;
    const radius = Math.min(nodes.length * 50, 500);

    const layoutedNodes = nodes.map((node, index) => {
      const angle = (index / nodes.length) * 2 * Math.PI;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);

      return {
        ...node,
        position: { x, y }
      };
    });

    return { nodes: layoutedNodes, edges };
  }

  /**
   * 应用径向布局
   * @param nodes 节点数组
   * @param edges 边数组
   * @returns 布局后的节点和边
   */
  private applyRadialLayout(nodes: Node[], edges: Edge[]): { nodes: Node[]; edges: Edge[] } {
    // 简单实现，实际项目中可使用专门的布局库
    const centerX = 500;
    const centerY = 300;
    const radius = Math.min(nodes.length * 30, 400);

    const layoutedNodes = nodes.map((node, index) => {
      const angle = (index / nodes.length) * 2 * Math.PI;
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);

      return {
        ...node,
        position: { x, y }
      };
    });

    return { nodes: layoutedNodes, edges };
  }

  /**
   * 获取资源信息
   * @param resourceId 资源ID
   * @returns 资源信息
   */
  private async getResourceById(resourceId: string): Promise<ResourceInfo | null> {
    // 检查缓存
    if (this.cachedResources.has(resourceId)) {
      return this.cachedResources.get(resourceId)!;
    }

    try {
      const resource = await this.resourceService.getResourceById(resourceId);

      if (resource) {
        this.cachedResources.set(resourceId, resource);
      }

      return resource;
    } catch (error) {
      console.error(`获取资源信息失败: ${resourceId}`, error);
      return null;
    }
  }

  /**
   * 获取资源依赖
   * @param resourceId 资源ID
   * @returns 依赖信息数组
   */
  private async getResourceDependencies(resourceId: string): Promise<DependencyInfo[]> {
    // 检查缓存
    if (this.cachedDependencies.has(resourceId)) {
      return this.cachedDependencies.get(resourceId)!;
    }

    try {
      const dependencies = await this.resourceService.getDependencies(resourceId);

      this.cachedDependencies.set(resourceId, dependencies);

      return dependencies;
    } catch (error) {
      console.error(`获取资源依赖失败: ${resourceId}`, error);
      return [];
    }
  }

  /**
   * 获取资源被依赖
   * @param resourceId 资源ID
   * @returns 被依赖信息数组
   */
  private async getResourceDependents(resourceId: string): Promise<DependencyInfo[]> {
    // 检查缓存
    if (this.cachedDependents.has(resourceId)) {
      return this.cachedDependents.get(resourceId)!;
    }

    try {
      const dependents = await this.resourceService.getDependents(resourceId);

      this.cachedDependents.set(resourceId, dependents);

      return dependents;
    } catch (error) {
      console.error(`获取资源被依赖失败: ${resourceId}`, error);
      return [];
    }
  }

  /**
   * 分析图
   * @param nodes 节点数组
   * @param edges 边数组
   * @param resources 资源列表
   * @returns 分析结果
   */
  private async analyzeGraph(nodes: Node[], edges: Edge[], resources: ResourceInfo[]): Promise<AnalysisResult['analysis']> {
    // 计算总资源数
    const totalResources = nodes.length;

    // 计算总依赖数
    const totalDependencies = edges.length;

    // 检测循环依赖
    const circularDependencies = this.detectCircularDependencies(nodes, edges);

    // 检测未使用资源
    const unusedResources = this.detectUnusedResources(nodes, edges);

    // 检测重复资源
    const duplicateResources = this.detectDuplicateResources(resources);

    // 检测大型资源
    const largeResources = this.detectLargeResources(resources);

    // 按类型统计资源
    const resourcesByType = this.countResourcesByType(resources);

    // 计算依赖链
    const dependencyChains = this.calculateDependencyChains(nodes, edges);

    // 计算加载顺序
    const loadOrder = this.calculateLoadOrder(nodes, edges);

    // 计算内存使用
    const memoryUsage = resources.reduce((sum, resource) => sum + resource.size, 0);

    // 资源大小映射
    const resourceSizes = resources.reduce((map, resource) => {
      map[resource.id] = resource.size;
      return map;
    }, {} as Record<string, number>);

    return {
      totalResources,
      totalDependencies,
      circularDependencies,
      unusedResources,
      duplicateResources,
      largeResources,
      resourcesByType,
      dependencyChains,
      loadOrder,
      memoryUsage,
      resourceSizes
    };
  }

  /**
   * 检测循环依赖
   * @param nodes 节点数组
   * @param edges 边数组
   * @returns 循环依赖数组
   */
  private detectCircularDependencies(nodes: Node[], edges: Edge[]): string[][] {
    const cycles: string[][] = [];
    const graph: Record<string, string[]> = {};

    // 构建图
    nodes.forEach(node => {
      graph[node.id] = [];
    });

    edges.forEach(edge => {
      graph[edge.source].push(edge.target);
    });

    // 对每个节点进行DFS
    nodes.forEach(node => {
      const visited = new Set<string>();
      const path: string[] = [];

      const dfs = (current: string, start: string) => {
        // 如果已在路径中，则找到循环
        if (path.includes(current)) {
          const cycleStart = path.indexOf(current);
          const cycle = path.slice(cycleStart).concat(current);

          // 检查是否已存在相同循环
          const cycleStr = cycle.sort().join(',');
          if (!cycles.some(c => c.sort().join(',') === cycleStr)) {
            cycles.push(cycle);
          }

          return;
        }

        // 如果已访问过，则跳过
        if (visited.has(current)) {
          return;
        }

        // 标记为已访问
        visited.add(current);

        // 添加到路径
        path.push(current);

        // 递归处理邻居
        for (const neighbor of graph[current] || []) {
          dfs(neighbor, start);
        }

        // 从路径中移除
        path.pop();
      };

      dfs(node.id, node.id);
    });

    return cycles;
  }

  /**
   * 检测未使用资源
   * @param nodes 节点数组
   * @param edges 边数组
   * @returns 未使用资源ID数组
   */
  private detectUnusedResources(nodes: Node[], edges: Edge[]): string[] {
    const usedAsTarget = new Set<string>();

    // 收集所有被依赖的资源
    edges.forEach(edge => {
      usedAsTarget.add(edge.target);
    });

    // 找出没有被依赖的资源
    return nodes
      .filter(node => !usedAsTarget.has(node.id))
      .map(node => node.id);
  }

  /**
   * 检测重复资源
   * @param resources 资源列表
   * @returns 重复资源组数组
   */
  private detectDuplicateResources(resources: ResourceInfo[]): string[][] {
    const duplicates: string[][] = [];
    const urlMap: Record<string, string[]> = {};

    // 按URL分组
    resources.forEach(resource => {
      const url = resource.url.toLowerCase();

      if (!urlMap[url]) {
        urlMap[url] = [];
      }

      urlMap[url].push(resource.id);
    });

    // 找出重复的URL
    for (const [url, ids] of Object.entries(urlMap)) {
      if (ids.length > 1) {
        duplicates.push(ids);
      }
    }

    return duplicates;
  }

  /**
   * 检测大型资源
   * @param resources 资源列表
   * @returns 大型资源ID数组
   */
  private detectLargeResources(resources: ResourceInfo[]): string[] {
    // 设置大型资源阈值（例如1MB）
    const threshold = 1024 * 1024;

    return resources
      .filter(resource => resource.size > threshold)
      .sort((a, b) => b.size - a.size)
      .map(resource => resource.id);
  }

  /**
   * 按类型统计资源
   * @param resources 资源列表
   * @returns 类型统计
   */
  private countResourcesByType(resources: ResourceInfo[]): Record<string, number> {
    const counts: Record<string, number> = {};

    resources.forEach(resource => {
      const type = resource.type;

      if (!counts[type]) {
        counts[type] = 0;
      }

      counts[type]++;
    });

    return counts;
  }

  /**
   * 计算依赖链
   * @param nodes 节点数组
   * @param edges 边数组
   * @returns 依赖链数组
   */
  private calculateDependencyChains(nodes: Node[], edges: Edge[]): string[][] {
    const chains: string[][] = [];
    const graph: Record<string, string[]> = {};

    // 构建图
    nodes.forEach(node => {
      graph[node.id] = [];
    });

    edges.forEach(edge => {
      graph[edge.source].push(edge.target);
    });

    // 对每个节点进行DFS
    nodes.forEach(node => {
      const path: string[] = [];

      const dfs = (current: string) => {
        // 添加到路径
        path.push(current);

        // 如果是叶子节点，则保存路径
        const neighbors = graph[current] || [];

        if (neighbors.length === 0) {
          chains.push([...path]);
        } else {
          // 递归处理邻居
          for (const neighbor of neighbors) {
            // 避免循环依赖
            if (!path.includes(neighbor)) {
              dfs(neighbor);
            }
          }
        }

        // 从路径中移除
        path.pop();
      };

      // 只对没有入边的节点开始DFS
      const hasIncomingEdges = edges.some(edge => edge.target === node.id);

      if (!hasIncomingEdges) {
        dfs(node.id);
      }
    });

    return chains;
  }

  /**
   * 计算加载顺序
   * @param nodes 节点数组
   * @param edges 边数组
   * @returns 加载顺序
   */
  private calculateLoadOrder(nodes: Node[], edges: Edge[]): string[] {
    const result: string[] = [];
    const visited = new Set<string>();
    const graph: Record<string, string[]> = {};

    // 构建图
    nodes.forEach(node => {
      graph[node.id] = [];
    });

    edges.forEach(edge => {
      graph[edge.source].push(edge.target);
    });

    // 拓扑排序
    const visit = (nodeId: string) => {
      // 如果已访问过，则跳过
      if (visited.has(nodeId)) {
        return;
      }

      // 标记为已访问
      visited.add(nodeId);

      // 递归处理依赖
      for (const depId of graph[nodeId] || []) {
        visit(depId);
      }

      // 添加到结果
      result.push(nodeId);
    };

    // 对每个节点进行拓扑排序
    nodes.forEach(node => {
      if (!visited.has(node.id)) {
        visit(node.id);
      }
    });

    return result.reverse();
  }

  /**
   * 生成优化建议
   * @param analysis 分析结果
   * @param resources 资源列表
   * @returns 优化建议数组
   */
  private generateOptimizationSuggestions(
    analysis: AnalysisResult['analysis'],
    resources: ResourceInfo[]
  ): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];

    // 处理循环依赖
    analysis.circularDependencies.forEach((cycle, index) => {
      suggestions.push({
        id: `circular-${index}`,
        type: 'circular',
        severity: 'high',
        description: `检测到循环依赖: ${cycle.join(' -> ')}`,
        resources: cycle,
        impact: '循环依赖可能导致加载问题和内存泄漏',
        solution: '重构资源关系，打破循环依赖，或使用弱依赖替代强依赖'
      });
    });

    // 处理未使用资源
    if (analysis.unusedResources.length > 0) {
      suggestions.push({
        id: 'unused',
        type: 'unused',
        severity: 'medium',
        description: `检测到 ${analysis.unusedResources.length} 个未使用资源`,
        resources: analysis.unusedResources,
        impact: '未使用资源占用存储空间，可能导致不必要的加载',
        solution: '考虑移除未使用资源，或将其标记为可选资源'
      });
    }

    // 处理重复资源
    analysis.duplicateResources.forEach((group, index) => {
      suggestions.push({
        id: `duplicate-${index}`,
        type: 'duplicate',
        severity: 'medium',
        description: `检测到重复资源: ${group.join(', ')}`,
        resources: group,
        impact: '重复资源导致存储和内存浪费',
        solution: '合并重复资源，使用单一实例'
      });
    });

    // 处理大型资源
    if (analysis.largeResources.length > 0) {
      suggestions.push({
        id: 'large',
        type: 'large',
        severity: 'low',
        description: `检测到 ${analysis.largeResources.length} 个大型资源`,
        resources: analysis.largeResources,
        impact: '大型资源可能导致加载时间延长和内存压力',
        solution: '考虑压缩资源，或使用延迟加载策略'
      });
    }

    // 长依赖链优化建议
    const longChains = analysis.dependencyChains.filter(chain => chain.length > 5);

    if (longChains.length > 0) {
      suggestions.push({
        id: 'long-chains',
        type: 'lazy',
        severity: 'low',
        description: `检测到 ${longChains.length} 个长依赖链`,
        resources: longChains.flat(),
        impact: '长依赖链可能导致加载延迟和级联失败',
        solution: '考虑使用延迟加载或预加载策略优化依赖链'
      });
    }

    return suggestions;
  }
}
